帮我设计**服务器管理**应用，用于监测服务器上应用环境的运行情况、管理服务器上指定目录的文件，各自再展开描述一下。

- **监测应用环境**：监测Nginx、PHP、MySQL 运行状态，资源占用的情况。
- **文件管理**：管理指定目录下的文件，例如查看、编辑、删除等，需要有一个功能设置需要管理的目录。



输出描述。

1. 功能：用列表描述（只描述要明确做的功能，不需要的不用描述）。
2. 交互流程：用列表描述


输出页面列表：
1. 按照功能节点列出具体的功能页面
2. 描述每个功能页面的具体功能点
3. 每个功能页面都以列表的方式输出，先输出页面的名称（例如：dashboard.html），然后在名称下输出具体功能点。


每个功能页面的需求敲定了，确定搭建静态页面的技术栈，界面是基于Bootstrap + FontAwesome 构建。

根据页面列表输出：
1、每个页面设计到的Bootstrap组件
2、每个页面设计到的FontAwesome图标
3、每个页面的布局设计


每个功能的基础布局设计基本上已经明确了。

输出：
1、每个页面交互所需要用到的第三方JS库（Bootstrap组件更多的是搭建页面，具体的交互还是找专门的第三方JS库）
2、所有页面通用的第三方JS库

上述的回复中提到一个需求：使用单页模式构建加载页面。

我认为可以简单点设计一个库，取名为**mpl.js**，意思是Monitor Page Loader。

大概流程：
1. 监测hash变化（不支持history 路由模式）
2. 根据hash路由，拼接成具体的页面url（例如：/monitor/view/states；/monitor/view/file之类）
3. 在主功能展示区域，内嵌一个iframe，将页面地址的内容通过iframe展示出来

输出：
1. mpl.js 库的设计思路（实例对象API的设计）
2. 考虑到部分用户的浏览器不是最新的，JS兼容ES5语法
3. 使用new mpl(options) 初始化
4. JS库的使用说明
