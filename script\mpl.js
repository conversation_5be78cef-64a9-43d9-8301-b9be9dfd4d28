(function (global, factory) {
  if (typeof define === 'function' && define.amd) {
    define([], factory);
  } else if (typeof module !== 'undefined' && module.exports) {
    module.exports = factory();
  } else {
    global.mpl = factory();
  }
})(this, function () {
  'use strict';

  /**
   * Monitor Page Loader构造函数
   * @param {Object} options 配置项
   */
  function mpl(options) {
    // 默认配置
    this.options = {
      container: '#app',
      basePath: '/monitor/view/',
      defaultPage: 'states',
      onLoad: null,
      onError: null,
    };

    // 合并用户配置
    for (var key in options) {
      if (options.hasOwnProperty(key)) {
        this.options[key] = options[key];
      }
    }

    this.init();
  }

  /**
   * 初始化路由监听
   */
  mpl.prototype.init = function () {
    var self = this;
    var container = document.querySelector(this.options.container);

    if (!container) {
      console.error('mpl.js: Container not found');
      return;
    }

    this.iframe = document.createElement('iframe');
    this.iframe.style.cssText = 'width:100%;height:100%;border:none;';
    container.appendChild(this.iframe);

    window.addEventListener('hashchange', function () {
      self.loadPage();
    });

    if (!window.location.hash) {
      window.location.hash = this.options.defaultPage;
    } else {
      this.loadPage();
    }
  };

  /**
   * 加载页面
   */
  mpl.prototype.loadPage = function () {
    var page = this.getCurrentPage();
    this.iframe.src = this.options.basePath + page + '.html';

    this.iframe.onload = function () {
      if (typeof this.options.onLoad === 'function') {
        this.options.onLoad(page);
      }
    }.bind(this);

    this.iframe.onerror = function () {
      if (typeof this.options.onError === 'function') {
        this.options.onError(page);
      }
    }.bind(this);
  };

  /**
   * 页面跳转
   * @param {string} page 目标页面
   */
  mpl.prototype.navigate = function (page) {
    window.location.hash = page;
  };

  /**
   * 获取当前页面
   * @returns {string}
   */
  mpl.prototype.getCurrentPage = function () {
    return window.location.hash.replace('#', '') || this.options.defaultPage;
  };

  /**
   * 重新加载当前页面
   */
  mpl.prototype.reload = function () {
    this.iframe.contentWindow.location.reload();
  };

  return mpl;
});