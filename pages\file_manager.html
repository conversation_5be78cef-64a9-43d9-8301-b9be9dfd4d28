<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理</title>
    <!-- 引入Bootstrap CSS -->
    <link rel="stylesheet" href="../libs/bootstrap/5.3.6/index.min.css">
    <!-- 引入FontAwesome -->
    <link rel="stylesheet" href="../libs/fontawesome/6.7.2/css/all.min.css">
    <style>
        .tree-view .list-group-item {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .tree-view .list-group-item:hover {
            background-color: #f8f9fa;
        }
        .tree-view .list-group-item.active {
            background-color: #0d6efd;
            color: white;
        }
        .file-icon {
            width: 24px;
            text-align: center;
        }
        .breadcrumb {
            background-color: #f8f9fa;
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
        }
        .table-hover tbody tr:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题和操作栏 -->
        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
            <h1 class="h2 mb-0"><i class="fas fa-folder-open me-2"></i>文件管理</h1>
            <div class="btn-group">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="fas fa-upload me-1"></i> 上传
                </button>
                <button class="btn btn-outline-secondary">
                    <i class="fas fa-folder-plus me-1"></i> 新建文件夹
                </button>
                <button class="btn btn-outline-secondary">
                    <i class="fas fa-sync-alt me-1"></i> 刷新
                </button>
            </div>
        </div>

        <div class="row">
            <!-- 目录树 -->
            <div class="col-md-3 border-right p-3" style="height: calc(100vh - 60px); overflow-y: auto;">
                <div class="list-group tree-view" id="directoryTree">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div><i class="fas fa-server me-2"></i>服务器根目录</div>
                        <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-ellipsis-v"></i></button>
                    </div>
                    <div class="list-group-item ps-5 d-flex justify-content-between align-items-center">
                        <div><i class="fas fa-folder me-2"></i>var</div>
                        <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-down"></i></button>
                    </div>
                    <div class="list-group-item ps-8 active d-flex justify-content-between align-items-center">
                        <div><i class="fas fa-folder-open me-2"></i>www</div>
                        <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-down"></i></button>
                    </div>
                    <div class="list-group-item ps-11 d-flex justify-content-between align-items-center">
                        <div><i class="fas fa-folder me-2"></i>html</div>
                        <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-right"></i></button>
                    </div>
                    <div class="list-group-item ps-11 d-flex justify-content-between align-items-center">
                        <div><i class="fas fa-folder me-2"></i>logs</div>
                        <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-right"></i></button>
                    </div>
                    <div class="list-group-item ps-5 d-flex justify-content-between align-items-center">
                        <div><i class="fas fa-folder me-2"></i>etc</div>
                        <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-right"></i></button>
                    </div>
                    <div class="list-group-item ps-5 d-flex justify-content-between align-items-center">
                        <div><i class="fas fa-folder me-2"></i>usr</div>
                        <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>
            </div>

            <!-- 文件列表区域 -->
            <div class="col-md-9 p-4" style="height: calc(100vh - 60px); overflow-y: auto;">
                <!-- 面包屑导航 -->
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="#"><i class="fas fa-server me-1"></i> 根目录</a></li>
                        <li class="breadcrumb-item"><a href="#"><i class="fas fa-folder me-1"></i> var</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><i class="fas fa-folder-open me-1"></i> www</li>
                    </ol>
                </nav>

                <!-- 文件搜索和筛选 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="input-group" style="width: 300px;">
                        <input type="text" class="form-control" placeholder="搜索文件..." aria-label="搜索文件">
                        <button class="btn btn-outline-secondary" type="button"><i class="fas fa-search"></i></button>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-sort me-1"></i> 排序方式
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sort-alpha-down me-1"></i> 名称 (A-Z)</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sort-alpha-up me-1"></i> 名称 (Z-A)</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sort-numeric-down me-1"></i> 大小 (小到大)</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sort-numeric-up me-1"></i> 大小 (大到小)</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sort-amount-down me-1"></i> 修改时间 (最新)</a></li>
                        </ul>
                    </div>
                </div>

                <!-- 文件列表 -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th scope="col"><i class="fas fa-file me-1"></i> 文件名</th>
                                <th scope="col" class="d-none d-md-table-cell"><i class="fas fa-hdd me-1"></i> 大小</th>
                                <th scope="col" class="d-none d-lg-table-cell"><i class="fas fa-clock me-1"></i> 修改时间</th>
                                <th scope="col" class="text-end"><i class="fas fa-cog me-1"></i> 操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 目录项 -->
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="file-icon me-2"><i class="fas fa-folder text-warning"></i></div>
                                        <div>
                                            <div>html</div>
                                            <div class="small text-muted d-md-none">4096 KB • 2023-10-15 09:30</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">4096 KB</td>
                                <td class="d-none d-lg-table-cell">2023-10-15 09:30</td>
                                <td class="text-end">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary"><i class="fas fa-folder-open"></i></button>
                                        <button class="btn btn-outline-secondary"><i class="fas fa-ellipsis-v"></i></button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="file-icon me-2"><i class="fas fa-folder text-warning"></i></div>
                                        <div>
                                            <div>logs</div>
                                            <div class="small text-muted d-md-none">8192 KB • 2023-10-15 10:15</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">8192 KB</td>
                                <td class="d-none d-lg-table-cell">2023-10-15 10:15</td>
                                <td class="text-end">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary"><i class="fas fa-folder-open"></i></button>
                                        <button class="btn btn-outline-secondary"><i class="fas fa-ellipsis-v"></i></button>
                                    </div>
                                </td>
                            </tr>

                            <!-- 文件项 -->
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="file-icon me-2"><i class="fas fa-file-code text-primary"></i></div>
                                        <div>
                                            <div>index.html</div>
                                            <div class="small text-muted d-md-none">2.4 KB • 2023-10-14 16:45</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">2.4 KB</td>
                                <td class="d-none d-lg-table-cell">2023-10-14 16:45</td>
                                <td class="text-end">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-outline-secondary"><i class="fas fa-download"></i></button>
                                        <button class="btn btn-outline-secondary"><i class="fas fa-ellipsis-v"></i></button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="file-icon me-2"><i class="fas fa-file-image text-danger"></i></div>
                                        <div>
                                            <div>banner.jpg</div>
                                            <div class="small text-muted d-md-none">128 KB • 2023-10-14 14:20</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">128 KB</td>
                                <td class="d-none d-lg-table-cell">2023-10-14 14:20</td>
                                <td class="text-end">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-outline-secondary"><i class="fas fa-download"></i></button>
                                        <button class="btn btn-outline-secondary"><i class="fas fa-ellipsis-v"></i></button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="file-icon me-2"><i class="fas fa-file-code text-info"></i></div>
                                        <div>
                                            <div>style.css</div>
                                            <div class="small text-muted d-md-none">5.7 KB • 2023-10-14 13:10</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">5.7 KB</td>
                                <td class="d-none d-lg-table-cell">2023-10-14 13:10</td>
                                <td class="text-end">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-outline-secondary"><i class="fas fa-download"></i></button>
                                        <button class="btn btn-outline-secondary"><i class="fas fa-ellipsis-v"></i></button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="file-icon me-2"><i class="fas fa-file-code text-warning"></i></div>
                                        <div>
                                            <div>app.js</div>
                                            <div class="small text-muted d-md-none">18.2 KB • 2023-10-14 10:05</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">18.2 KB</td>
                                <td class="d-none d-lg-table-cell">2023-10-14 10:05</td>
                                <td class="text-end">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-outline-secondary"><i class="fas fa-download"></i></button>
                                        <button class="btn btn-outline-secondary"><i class="fas fa-ellipsis-v"></i></button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页控件 -->
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1"><i class="fas fa-chevron-left"></i></a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#"><i class="fas fa-chevron-right"></i></a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 上传文件模态框 -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel"><i class="fas fa-upload me-2"></i>上传文件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="card border-primary text-center p-5 mb-3">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <p>拖放文件到此处或点击选择文件</p>
                        <input type="file" class="form-control d-none" id="fileInput" multiple>
                        <button class="btn btn-outline-primary mt-2" onclick="document.getElementById('fileInput').click()">选择文件</button>
                    </div>
                    <div id="uploadFilesList"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary">开始上传</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="../libs/bootstrap/5.3.6/index.min.js"></script>
    <script>
        // 目录树交互
        document.addEventListener('DOMContentLoaded', function() {
            // 目录项点击事件
            const dirItems = document.querySelectorAll('.tree-view .list-group-item');
            dirItems.forEach(item => {
                if(!item.querySelector('.fa-ellipsis-v')) {
                    item.addEventListener('click', function() {
                        // 移除其他项的active类
                        dirItems.forEach(i => i.classList.remove('active'));
                        // 为当前项添加active类
                        this.classList.add('active');
                    });
                }
            });

            // 文件项点击事件
            const fileRows = document.querySelectorAll('tbody tr');
            fileRows.forEach(row => {
                row.addEventListener('click', function(e) {
                    // 如果点击的是按钮，不触发行选择
                    if(e.target.closest('button')) return;
                    // 切换行选中状态
                    this.classList.toggle('table-active');
                });
            });

            // 文件上传处理
            const fileInput = document.getElementById('fileInput');
            const uploadFilesList = document.getElementById('uploadFilesList');

            fileInput.addEventListener('change', function() {
                uploadFilesList.innerHTML = '';
                Array.from(this.files).forEach(file => {
                    const fileSize = (file.size / 1024).toFixed(1) + ' KB';
                    const fileItem = document.createElement('div');
                    fileItem.className = 'd-flex justify-content-between align-items-center mb-2 p-2 border rounded';
                    fileItem.innerHTML = `
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file me-2"></i>
                            <div>
                                <div>${file.name}</div>
                                <div class="small text-muted">${fileSize}</div>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-danger"><i class="fas fa-times"></i></button>
                    `;
                    uploadFilesList.appendChild(fileItem);

                    // 删除文件项事件
                    fileItem.querySelector('button').addEventListener('click', function() {
                        fileItem.remove();
                    });
                });
            });
        });
    </script>
</body>
</html>