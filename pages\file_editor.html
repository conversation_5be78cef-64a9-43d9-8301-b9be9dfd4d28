<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件编辑器</title>
    <!-- 引入Bootstrap CSS -->
    <link rel="stylesheet" href="../libs/bootstrap/5.3.6/index.min.css">
    <!-- 引入FontAwesome -->
    <link rel="stylesheet" href="../libs/fontawesome/6.7.2/css/all.min.css">
    <style>
        .editor-toolbar {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0.5rem 1rem;
        }
        #code-editor {
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        .large-file-alert {
            position: sticky;
            top: 0;
            z-index: 1020;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 顶部导航 -->
        <nav class="navbar navbar-light bg-light border-bottom">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-code me-2"></i>文件编辑器
                </a>
                <div class="d-flex align-items-center">
                    <span class="me-3 text-muted" id="file-path">/var/www/html/index.html</span>
                    <button class="btn btn-sm btn-outline-secondary me-2" id="close-editor">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                </div>
            </div>
        </nav>

        <!-- 大文件警告 -->
        <div class="alert alert-warning d-none large-file-alert" id="large-file-alert">
            <i class="fas fa-exclamation-triangle me-2"></i> <strong>警告:</strong> 检测到大文件，已禁用编辑功能以保证性能。
        </div>

        <!-- 编辑器工具栏 -->
        <div class="editor-toolbar d-flex flex-wrap justify-content-between align-items-center">
            <div class="btn-group me-2 mb-2 mb-md-0">
                <button class="btn btn-primary" id="save-btn">
                    <i class="fas fa-save me-1"></i> 保存
                </button>
                <button class="btn btn-outline-secondary" id="export-btn">
                    <i class="fas fa-file-export me-1"></i> 导出
                </button>
            </div>
            <div class="d-flex flex-wrap align-items-center">
                <div class="me-3 mb-2 mb-md-0">
                    <span class="text-muted me-1">编码:</span>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            UTF-8
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">UTF-8</a></li>
                            <li><a class="dropdown-item" href="#">GBK</a></li>
                            <li><a class="dropdown-item" href="#">ISO-8859-1</a></li>
                        </ul>
                    </div>
                </div>
                <div class="mb-2 mb-md-0">
                    <span class="text-muted me-1">行号:</span>
                    <span id="line-count">1</span>
                </div>
            </div>
        </div>

        <!-- 代码编辑器区域 -->
        <div class="p-3">
            <div id="code-editor" contenteditable="true" style="height: calc(100vh - 160px); min-height: 400px; padding: 1rem;">
&lt;!DOCTYPE html&gt;
&lt;html lang="zh-CN"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;示例页面&lt;/title&gt;
    &lt;link rel="stylesheet" href="../libs/bootstrap/5.3.6/index.min.css"&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="container mt-5"&gt;
        &lt;h1 class="text-center mb-4"&gt;欢迎使用系统监控平台&lt;/h1&gt;
        &lt;p class="text-center text-muted"&gt;这是一个使用Bootstrap构建的示例页面&lt;/p&gt;
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="../libs/bootstrap/5.3.6/index.min.js"></script>
    <script>
        // 文件编辑器功能实现
        document.addEventListener('DOMContentLoaded', function() {
            const codeEditor = document.getElementById('code-editor');
            const saveBtn = document.getElementById('save-btn');
            const exportBtn = document.getElementById('export-btn');
            const closeBtn = document.getElementById('close-editor');
            const lineCount = document.getElementById('line-count');
            const largeFileAlert = document.getElementById('large-file-alert');

            // 监听编辑器内容变化，更新行号
            codeEditor.addEventListener('input', updateLineCount);

            // 初始化行号
            updateLineCount();

            // 模拟大文件检测
            const contentLength = codeEditor.innerText.length;
            if (contentLength > 10000) {
                largeFileAlert.classList.remove('d-none');
                codeEditor.contentEditable = 'false';
                codeEditor.classList.add('bg-light', 'text-muted');
            }

            // 保存按钮功能
            saveBtn.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> 保存中...';
                this.disabled = true;

                // 模拟保存操作
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-save me-1"></i> 保存';
                    this.disabled = false;

                    // 显示保存成功提示
                    const toast = new bootstrap.Toast(document.createElement('div'));
                    const toastEl = document.createElement('div');
                    toastEl.className = 'toast align-items-center text-white bg-success border-0 position-fixed bottom-0 end-0 m-3';
                    toastEl.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="fas fa-check-circle me-1"></i> 文件保存成功
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    `;
                    document.body.appendChild(toastEl);
                    const newToast = new bootstrap.Toast(toastEl);
                    newToast.show();

                    // 3秒后自动移除提示
                    setTimeout(() => { toastEl.remove(); }, 3000);
                }, 1000);
            });

            // 导出按钮功能
            exportBtn.addEventListener('click', function() {
                const fileName = document.getElementById('file-path').textContent.split('/').pop();
                const blob = new Blob([codeEditor.innerText], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });

            // 关闭编辑器
            closeBtn.addEventListener('click', function() {
                if (confirm('确定要关闭编辑器吗？未保存的更改将会丢失。')) {
                    window.location.href = 'file_manager.html';
                }
            });

            // 更新行号函数
            function updateLineCount() {
                const lines = codeEditor.innerText.split('\n').length;
                lineCount.textContent = lines;
            }
        });
    </script>
</body>
</html>