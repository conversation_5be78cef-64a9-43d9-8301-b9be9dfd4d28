### 第三方JS库配置方案

#### 一、分页面专用库

1. **dashboard.html**
   - `Chart.js` (v3.9.1) - 绘制服务状态趋势图
   - `Socket.IO` (v4.5.4) - 实时状态推送
   - `CountUp.js` (v2.6.0) - 数字滚动动画

2. **nginx_status.html / php_status.html / mysql_status.html**
   - `ApexCharts` (v3.35.0) - 高级资源监控图表
   - `Moment.js` (v2.29.4) - 时间格式化（日志时间戳）
   - `js-url` (v2.5.3) - URL参数解析（时间范围筛选）

3. **file_manager.html**
   - `jstree` (v3.3.12) - 增强版目录树交互
   - `DataTables` (v1.13.4) - 文件列表分页/排序/搜索
   - `clipboard.js` (v2.0.11) - 文件路径复制功能

4. **file_editor.html**
   - `Monaco Editor` (v0.40.0) - 专业代码编辑器
   - `jsdiff` (v5.1.0) - 文件修改对比
   - `FileSaver.js` (v2.0.5) - 本地保存功能

5. **upload_zone.html**
   - `Dropzone` (v5.9.3) - 拖拽上传组件
   - `SparkMD5` (v3.0.2) - 文件分片MD5计算
   - `Resumable.js` (v1.1.0) - 断点续传支持

6. **logs.html**
   - `Tabulator` (v5.4.3) - 高级日志表格展示
   - `Papa Parse` (v5.3.2) - CSV导出处理
   - `daterangepicker` (v3.1.0) - 时间范围选择器

#### 二、全局通用库

1. **核心依赖**
   - `jQuery` (v3.6.4) - DOM操作基础
   - `Popper.js` (v2.11.8) - 工具提示/弹出框依赖
   - `axios` (v1.3.4) - HTTP请求库

2. **UI增强**
   - `toastr` (v2.1.4) - 操作通知提示
   - `sweetalert2` (v11.7.3) - 美观的确认对话框
   - `animate.css` (v4.1.1) - 交互动画效果

3. **工具类**
   - `lodash` (v4.17.21) - 实用工具函数
   - `validator` (v13.7.0) - 表单验证
   - `store.js` (v2.0.12) - 本地存储管理

4. **安全相关**
   - `jsencrypt` (v3.3.2) - 敏感数据加密
   - `DOMPurify` (v3.0.3) - XSS防护

#### 三、推荐CDN配置
```html
<!-- 全局头部加载 -->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios@1.3.4/dist/axios.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>

<!-- 按需加载示例（文件管理页） -->
<script src="https://cdn.jsdelivr.net/npm/jstree@3.3.12/dist/jstree.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables@1.13.4/js/jquery.dataTables.min.js"></script>
```

#### 四、版本控制建议
1. 生产环境锁定小版本号（如axios@1.3.x）
2. 使用`integrity`校验：
```html
<script 
  src="https://cdn.example.com/lib.js"
  integrity="sha384-xxxx"
  crossorigin="anonymous"></script>
```

#### 五、初始化代码示例
```javascript
// 全局配置示例
toastr.options = {
  positionClass: "toast-bottom-right",
  preventDuplicates: true
};

// 通用错误处理
axios.interceptors.response.use(null, error => {
  toastr.error(error.response?.data?.message || '请求失败');
  return Promise.reject(error);
});
```

> **注意事项**：
> 1. 监控类页面优先考虑WebSocket实时更新
> 2. 文件操作相关库需配合沙箱API使用
> 3. 移动端需额外引入触摸事件库（如hammer.js）