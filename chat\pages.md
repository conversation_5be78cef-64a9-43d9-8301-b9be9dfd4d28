### 页面列表及功能点说明

---

#### 1. **dashboard.html** - 主监控面板
- 聚合展示 Nginx/PHP/MySQL 的实时状态卡片  
- 异常服务红色高亮标记（停止/资源超阈值）  
- 点击卡片跳转到对应服务的详情页  
- 侧边栏显示最近访问的目录（仅文件管理模块使用后显示）  

---

#### 2. **nginx_status.html** - Nginx 监控页
- 显示运行状态（运行中/停止）及开关按钮  
- 实时折线图展示 CPU/内存占用率（支持时间范围选择）  
- 错误日志摘要（最后10条，按时间倒序）  
- 服务重启快捷按钮（需管理员权限）  

---

#### 3. **php_status.html** - PHP 监控页
- PHP-FPM 进程数统计（活跃/空闲进程）  
- 资源消耗仪表盘（CPU/内存/线程）  
- PHP 版本及已加载扩展列表  
- 进程异常崩溃时自动刷新状态  

---

#### 4. **mysql_status.html** - MySQL 监控页
- 服务状态及端口检测  
- 实时连接数/查询速率计数器  
- 数据库存储空间环形图（按库拆分）  
- 慢查询日志入口（跳转到日志查看页）  

---

#### 5. **file_manager.html** - 文件管理主页
- 左侧目录树（仅显示白名单目录，禁止展开系统路径）  
- 右侧文件列表（分页展示当前目录内容）  
- 顶部操作栏：上传/下载/删除/搜索按钮  
- 面包屑导航显示当前路径（可点击跳转上级）  

---

#### 6. **file_editor.html** - 文件编辑器
- 代码编辑器核心功能（语法高亮/行号/缩进）  
- 顶部警告栏（10MB以上文件禁止编辑）  
- 保存时自动生成 `.bak` 备份文件  
- 实时显示文件编码格式（支持UTF-8/GBK切换）  

---

#### 7. **directory_settings.html** - 目录设置页
- 白名单目录列表（显示路径/添加时间）  
- 目录添加表单（输入框+浏览按钮，后端校验合法性）  
- 目录删除功能（需二次确认，同时删除操作日志）  
- 系统目录自动过滤提示（如输入 `/etc` 时禁用添加按钮）  

---

#### 8. **logs.html** - 操作日志页
- 表格展示操作记录（时间/用户/操作类型/路径）  
- 支持按时间范围筛选（最近7天/自定义）  
- 高危操作红色标记（如删除系统文件尝试）  
- 日志导出按钮（CSV格式）  

---

#### 9. **upload_zone.html** - 上传专用页
- 拖拽文件区域（显示最大单文件限制）  
- 多文件队列管理（取消/重试单个文件）  
- 实时进度条（速度/剩余时间估算）  
- 上传完成自动刷新文件列表  

---

#### 关键页面关系说明：
- 所有监控页通过 `status_api` 实时获取数据  
- 文件操作页通过 `sandbox_api` 代理执行系统调用  
- 页面跳转逻辑：`dashboard.html` → 各服务状态页 → 返回按钮保持层级

#### 页面跳转逻辑说明：

以下是使用 Mermaid 语法绘制的关键页面关系流程图，重点展示页面跳转逻辑和数据交互方式：

```mermaid
graph LR
    A[dashboard.html] -->|点击服务卡片| B[nginx_status.html]
    A -->|点击服务卡片| C[php_status.html]
    A -->|点击服务卡片| D[mysql_status.html]
    A -->|侧边栏入口| E[file_manager.html]
    
    E -->|点击编辑按钮| F[file_editor.html]
    E -->|设置按钮| G[directory_settings.html]
    E -->|上传按钮| H[upload_zone.html]
    E -->|日志按钮| I[logs.html]
    
    B -->|返回按钮| A
    C -->|返回按钮| A
    D -->|返回按钮| A
    
    subgraph 数据接口
        J(status_api)
        K(sandbox_api)
    end
    
    A <-->|实时拉取状态| J
    B <-->|获取Nginx数据| J
    C <-->|获取PHP数据| J
    D <-->|获取MySQL数据| J
    E <-->|文件操作代理| K
    F <-->|保存/读取文件| K
    G <-->|目录白名单管理| K
    H <-->|上传文件代理| K
    I <-->|日志查询| K
```

### 流程图说明：
1. **页面跳转关系**（实线箭头）：
   - 主面板可跳转到三大服务监控页和文件管理页
   - 文件管理页作为枢纽连接所有子功能页（编辑器/设置/上传/日志）

2. **数据交互关系**（虚线双向箭头）：
   - 所有状态监控页共用 `status_api` 获取实时数据
   - 文件相关操作通过 `sandbox_api` 代理执行系统调用

3. **安全隔离设计**：
   - 监控模块与文件模块使用独立API接口
   - 所有文件操作必须经过沙箱代理（橙色标注）

4. **返回逻辑**：
   - 所有子页面保留返回主面板的路径
   - 面包屑导航未在流程图中体现（实际存在于文件管理相关页面）
