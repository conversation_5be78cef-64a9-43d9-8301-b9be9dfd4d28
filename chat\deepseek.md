### 服务器管理应用设计方案

#### 1. 功能列表
**A. 应用环境监测模块**
- Nginx 状态监测  
  - 实时显示运行状态（运行中/停止）
  - 监控 CPU/内存占用率
- PHP 状态监测  
  - 实时显示 PHP-FPM 进程状态
  - 监控 PHP 进程资源消耗
  - 显示当前 PHP 版本和扩展状态
- MySQL 状态监测  
  - 实时显示服务状态
  - 监控查询速率和连接数
  - 显示数据库存储空间占用
- 统一监控面板  
  - 聚合展示三项服务的实时状态
  - 异常状态自动告警（红色标记）

**B. 文件管理模块**
- 目录管理  
  - 设置/修改监控目录白名单（判断该目录是否为系统目录，是则不允许条件）
  - 删除目录：有可能该目录不使用了
- 文件操作  
  - 当前目录下的文件列表分页展示（含属性信息），不需要递归查询子目录的文件
  - 文本文件在线编辑与保存
  - 文件/目录删除（含二次确认）
  - 批量上传/下载功能
  - 文件搜索（按名称/类型）
- 安全控制  
  - 操作日志记录（谁/何时/操作）

#### 2. 交互流程
**A. 应用环境监测流程**
1. **首页加载**  
   - 自动检测 Nginx/PHP/MySQL 状态
   - 三服务状态卡片式平铺展示
2. **详情查看**  
   - 点击服务卡片 → 展开详细资源占用图表
   - 悬浮显示实时数据（如 MySQL 当前连接数）
3. **异常处理**  
   - 服务停止时显示红色警示
   - 资源超阈值时弹出告警提示

**B. 文件管理流程**
```mermaid
graph TD
    A[进入文件管理] --> B{目录设置}
    B -- 新用户 --> C[引导设置监控目录]
    B -- 已设置 --> D[显示目录树]
    D --> E[选择目录]
    E --> F[显示文件列表]
    F --> G[选择操作]
    G -- 查看/编辑 --> H[在线编辑器]
    G -- 删除 --> I[确认对话框]
    G -- 上传 --> J[拖拽文件区域]
    G -- 下载 --> K[自动触发下载]
    H --> M[保存时备份原文件]
    I --> N[记录删除日志]
    J --> O[显示上传进度]
```

**详细交互：**
1. **目录设置**  
   - 首次使用强制设置监控目录
   - 路径输入框（后端负责监测目录是否存在）
   - 目录清单以列表形式展示，点击其中一条目录记录，查看（编辑）当前目录下的文件和目录（子目录点击进去也是查看当前目录下的文件清单）

2. **文件操作流程**  
   - **查看文件**：点击文件名 → 新标签页打开预览  
   - **编辑文件**：点击编辑图标 → 弹出代码编辑器（语法高亮） → 保存时版本备份  
   - **删除操作**：勾选文件 → 点击删除 → 二次确认（显示路径和大小）  
   - **上传文件**：拖拽至操作区 或 点击上传按钮 → 实时进度显示  
   - **目录内容**：仅显示当前目录下的文件和目录（不递归），要有个导航区展示当前查看的目录，点击某个目录节点，就展示某个目录下的文件清单

3. **权限控制**  
   - 管理员：全功能权限 （只有一个管理员角色）

**关键交互细节：**
- 文件列表显示：图标区分文件类型 + 实时显示大小/修改时间
- 大文件操作：超过 10MB 文件禁止在线编辑
- 错误处理：操作失败时显示具体原因（如权限不足）
- 历史记录：侧边栏显示最近访问的 5 个目录

> **安全设计重点**：所有文件操作通过沙箱代理执行，禁止直接系统调用；设置目录白名单时自动过滤敏感路径（如 /root）；会话超时自动锁定。