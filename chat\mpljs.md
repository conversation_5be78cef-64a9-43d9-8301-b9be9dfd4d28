### **mpl.js (Monitor Page Loader) 设计文档**  

#### **1. 设计思路**  
`mpl.js` 是一个轻量级单页应用（SPA）加载器，基于 **Hash 路由** 和 **iframe 内嵌** 实现页面切换，适用于监控类后台管理系统。  

**核心功能：**  
- 监听 `hashchange` 事件，动态加载目标页面  
- 通过 `iframe` 嵌入目标页面，避免整页刷新  
- 支持 ES5 语法，兼容旧版浏览器  
- 提供 API 控制路由跳转、页面加载、错误处理  

---

### **2. API 设计**  
#### **构造函数 `new mpl(options)`**  
| 参数 | 类型 | 说明 |
|------|------|------|
| `container` | `string` | 必填，iframe 挂载的 DOM 容器（如 `#app`） |
| `basePath` | `string` | 页面基础路径（如 `/monitor/view/`） |
| `defaultPage` | `string` | 默认页面（如 `states`，对应 `#states`） |
| `onLoad` | `function` | 页面加载完成回调 |
| `onError` | `function` | 加载失败回调 |

#### **实例方法**  
| 方法 | 说明 |
|------|------|
| `navigate(page)` | 跳转到指定页面（如 `mpl.navigate('file')`） |
| `getCurrentPage()` | 获取当前页面 hash（如 `states`） |
| `reload()` | 重新加载当前页面 |

#### **事件监听**  
| 事件 | 说明 |
|------|------|
| `hashchange` | 监听路由变化（浏览器原生事件） |

---

### **3. 代码实现（ES5 兼容）**  
```javascript
/**
 * Monitor Page Loader (mpl.js)
 * @param {Object} options 配置项
 */
function mpl(options) {
  // 默认配置
  this.options = {
    container: '#app',
    basePath: '/monitor/view/',
    defaultPage: 'states',
    onLoad: null,
    onError: null,
  };

  // 合并用户配置
  for (var key in options) {
    if (options.hasOwnProperty(key)) {
      this.options[key] = options[key];
    }
  }

  // 初始化
  this.init();
}

/**
 * 初始化路由监听
 */
mpl.prototype.init = function () {
  var self = this;
  var container = document.querySelector(this.options.container);

  // 确保容器存在
  if (!container) {
    console.error('mpl.js: Container not found');
    return;
  }

  // 创建 iframe
  this.iframe = document.createElement('iframe');
  this.iframe.style.width = '100%';
  this.iframe.style.height = '100%';
  this.iframe.style.border = 'none';
  container.appendChild(this.iframe);

  // 监听 hash 变化
  window.addEventListener('hashchange', function () {
    self.loadPage();
  });

  // 初始加载
  if (!window.location.hash) {
    window.location.hash = this.options.defaultPage;
  } else {
    this.loadPage();
  }
};

/**
 * 加载页面
 */
mpl.prototype.loadPage = function () {
  var page = this.getCurrentPage();
  var url = this.options.basePath + page + '.html';

  this.iframe.src = url;
  this.iframe.onload = function () {
    if (typeof this.options.onLoad === 'function') {
      this.options.onLoad(page);
    }
  }.bind(this);

  this.iframe.onerror = function () {
    if (typeof this.options.onError === 'function') {
      this.options.onError(page);
    }
  }.bind(this);
};

/**
 * 跳转页面
 * @param {string} page 目标页面
 */
mpl.prototype.navigate = function (page) {
  window.location.hash = page;
};

/**
 * 获取当前页面
 * @returns {string} 当前 hash
 */
mpl.prototype.getCurrentPage = function () {
  return window.location.hash.replace('#', '') || this.options.defaultPage;
};

/**
 * 重新加载当前页面
 */
mpl.prototype.reload = function () {
  this.iframe.contentWindow.location.reload();
};
```

---

### **4. 使用说明**  
#### **（1）引入 mpl.js**  
```html
<div id="app"></div>
<script src="mpl.js"></script>
```

#### **（2）初始化**  
```javascript
var loader = new mpl({
  container: '#app',
  basePath: '/monitor/view/',
  defaultPage: 'dashboard',
  onLoad: function (page) {
    console.log('Loaded:', page);
  },
  onError: function (page) {
    console.error('Failed to load:', page);
  }
});
```

#### **（3）手动跳转**  
```javascript
// 跳转到 file 页面
loader.navigate('file');

// 获取当前页面
console.log(loader.getCurrentPage());

// 重新加载当前页面
loader.reload();
```

#### **（4）HTML 链接跳转**  
```html
<a href="#dashboard">Dashboard</a>
<a href="#file">File Manager</a>
```

---

### **5. 兼容性**  
- 支持 **IE9+** 及所有现代浏览器  
- 依赖 `hashchange` 事件（IE8+ 支持）  
- 无外部依赖（纯原生 JS）  

---

### **6. 优化建议**  
1. **预加载**：提前加载常用页面（如 `dashboard`）  
2. **缓存**：使用 `localStorage` 存储已加载页面  
3. **加载动画**：在 `onLoad` 回调里显示/隐藏加载动画  

---

### **总结**  
`mpl.js` 提供了一种 **轻量级 iframe 单页方案**，适合监控类后台系统，兼容旧浏览器，API 简单易用。