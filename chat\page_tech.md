### 技术栈实现方案（Bootstrap 5 + FontAwesome 6）

#### 1. **dashboard.html** - 主监控面板
**Bootstrap组件**：
- 卡片组件（服务状态卡片）
- 警报组件（异常状态提示）
- 侧边栏导航（Collapse组件）
- 进度条（资源占用可视化）

**FontAwesome图标**：
- `fa-server`（服务器图标）
- `fa-triangle-exclamation`（警告图标）
- `fa-arrow-right`（跳转箭头）
- `fa-history`（最近访问目录）

**布局设计**：
```html
<div class="container-fluid">
  <div class="row">
    <!-- 侧边栏 -->
    <nav class="col-md-2 d-none d-md-block sidebar">
      <div class="recent-dirs">...</div>
    </nav>
    
    <!-- 主内容区 -->
    <main class="col-md-10">
      <div class="row row-cols-1 row-cols-md-3 g-4">
        <!-- Nginx状态卡片 -->
        <div class="col">
          <div class="card h-100">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-network-wired"></i> Nginx</h5>
              <div class="status-indicator"></div>
            </div>
          </div>
        </div>
        <!-- 其他服务卡片... -->
      </div>
    </main>
  </div>
</div>
```

#### 2. **nginx_status.html** - Nginx监控页
**Bootstrap组件**：
- 选项卡（切换CPU/内存视图）
- 折线图容器（Card组件）
- 按钮组（服务操作按钮）
- 列表组（错误日志展示）

**FontAwesome图标**：
- `fa-chart-line`（图表图标）
- `fa-power-off`（服务开关）
- `fa-rotate`（重启按钮）
- `fa-bug`（错误日志）

**布局设计**：
```html
<div class="container">
  <!-- 状态控制区 -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="btn-group">
        <button class="btn btn-danger"><i class="fas fa-power-off"></i> 停止</button>
      </div>
    </div>
  </div>
  
  <!-- 图表区 -->
  <div class="row">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <ul class="nav nav-tabs card-header-tabs">
            <li class="nav-item">
              <a class="nav-link active" href="#cpu-tab">CPU占用</a>
            </li>
          </ul>
        </div>
        <div class="card-body">
          <div class="chart-container"></div>
        </div>
      </div>
    </div>
  </div>
</div>
```

#### 3. **file_manager.html** - 文件管理主页
**Bootstrap组件**：
- 树状导航（使用List group改造）
- 分页组件
- 面包屑导航
- 模态框（删除确认）

**FontAwesome图标**：
- `fa-folder`（目录图标）
- `fa-file-code`（代码文件）
- `fa-file-image`（图片文件）
- `fa-trash`（删除按钮）

**布局设计**：
```html
<div class="container-fluid">
  <div class="row">
    <!-- 目录树 -->
    <div class="col-md-3">
      <div class="list-group tree-view">
        <a href="#" class="list-group-item">
          <i class="fas fa-folder"></i> /var/www
        </a>
      </div>
    </div>
    
    <!-- 文件列表 -->
    <div class="col-md-9">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">...</ol>
      </nav>
      
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th><i class="fas fa-file"></i> 文件名</th>
              <th>大小</th>
              <th>修改时间</th>
            </tr>
          </thead>
          <tbody>...</tbody>
        </table>
      </div>
      
      <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">...</ul>
      </nav>
    </div>
  </div>
</div>
```

#### 4. **file_editor.html** - 文件编辑器
**Bootstrap组件**：
- 代码编辑器容器（Card组件）
- 按钮工具栏
- 警告框（大文件提示）
- 下拉菜单（编码选择）

**FontAwesome图标**：
- `fa-code`（编辑器图标）
- `fa-save`（保存按钮）
- `fa-file-export`（导出按钮）
- `fa-language`（编码选择）

**布局设计**：
```html
<div class="container">
  <div class="alert alert-warning d-none" id="large-file-alert">
    <i class="fas fa-exclamation-triangle"></i> 大文件已禁用编辑
  </div>
  
  <div class="card">
    <div class="card-header">
      <div class="btn-toolbar">
        <div class="btn-group me-2">
          <button class="btn btn-primary">
            <i class="fas fa-save"></i> 保存
          </button>
        </div>
      </div>
    </div>
    <div class="card-body">
      <div id="code-editor" style="height: 70vh;"></div>
    </div>
  </div>
</div>
```

#### 5. **upload_zone.html** - 上传专用页
**Bootstrap组件**：
- 拖放区域（Card组件改造）
- 进度条组件
- 列表组（上传队列）
- 工具提示（悬停提示）

**FontAwesome图标**：
- `fa-cloud-upload-alt`（上传图标）
- `fa-times-circle`（取消上传）
- `fa-spinner`（加载动画）
- `fa-check-circle`（完成标记）

**布局设计**：
```html
<div class="container">
  <div class="card border-primary drag-area">
    <div class="card-body text-center py-5">
      <i class="fas fa-cloud-upload-alt fa-5x mb-3"></i>
      <h4>拖放文件到此处</h4>
    </div>
  </div>
  
  <div class="mt-4">
    <h5>上传队列</h5>
    <ul class="list-group" id="upload-queue">
      <li class="list-group-item">
        <div class="d-flex justify-content-between">
          <span>example.jpg</span>
          <div class="progress" style="width: 60%">...</div>
          <button class="btn btn-sm btn-danger">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </li>
    </ul>
  </div>
</div>
```

### 通用设计规范
1. **响应式断点**：
   - 侧边栏在`md`断点以下隐藏
   - 卡片布局在`sm`断点切换为垂直堆叠

2. **颜色方案**：
   - 主色：`primary`（Bootstrap蓝色）
   - 危险操作：`danger`（红色）
   - 成功状态：`success`（绿色）

3. **交互增强**：
   - 所有按钮添加悬停效果
   - 表格行添加`hover`高亮
   - 禁用状态添加`disabled`类

4. **图标使用原则**：
   - 操作按钮必须带图标
   - 状态图标配合颜色使用
   - 文件类型使用对应图标

> 实现建议：使用Bootstrap的JS组件增强交互（如Toast提示操作结果），通过FontAwesome的`fa-fw`类保持图标等宽对齐。对于复杂组件如代码编辑器，建议集成CodeMirror或Monaco Editor。