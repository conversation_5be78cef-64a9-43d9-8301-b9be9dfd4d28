<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传</title>
    <!-- 引入Bootstrap CSS -->
    <link rel="stylesheet" href="../libs/bootstrap/5.3.6/index.min.css">
    <!-- 引入FontAwesome -->
    <link rel="stylesheet" href="../libs/fontawesome/6.7.2/css/all.min.css">
    <style>
        .drag-area {
            border: 2px dashed #0d6efd;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .drag-area:hover,
        .drag-area.dragover {
            background-color: rgba(13, 110, 253, 0.05);
            border-color: #0a58ca;
        }
        .upload-queue-item {
            transition: all 0.2s;
        }
        .upload-queue-item:hover {
            background-color: #f8f9fa;
        }
        .progress {
            height: 8px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- 页面标题和返回按钮 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2"><i class="fas fa-cloud-upload-alt me-2"></i>文件上传</h1>
            <a href="file_manager.html" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> 返回文件管理
            </a>
        </div>

        <!-- 拖放区域 -->
        <div class="card border-primary drag-area mb-4" id="dragArea">
            <div class="card-body text-center py-5">
                <i class="fas fa-cloud-upload-alt fa-5x mb-3 text-primary"></i>
                <h4 class="mb-2">拖放文件到此处</h4>
                <p class="text-muted mb-4">或</p>
                <label class="btn btn-primary">
                    <i class="fas fa-folder-open me-1"></i> 选择文件
                    <input type="file" id="fileInput" class="d-none" multiple>
                </label>
                <p class="text-muted mt-3 small">支持格式: JPG, PNG, GIF, HTML, CSS, JS, TXT | 最大单个文件: 100MB</p>
            </div>
        </div>

        <!-- 上传队列 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><i class="fas fa-list-ol me-2"></i>上传队列</h5>
                <button class="btn btn-sm btn-outline-danger" id="clearQueueBtn">
                    <i class="fas fa-trash me-1"></i> 清空队列
                </button>
            </div>
            <div class="card-body p-0">
                <ul class="list-group list-group-flush" id="uploadQueue">
                    <!-- 上传队列项将通过JavaScript动态添加 -->
                    <li class="list-group-item text-center text-muted py-5" id="emptyQueueMessage">
                        <i class="fas fa-inbox fa-3x mb-2"></i>
                        <p>上传队列为空</p>
                    </li>
                </ul>
            </div>
            <div class="card-footer bg-transparent d-flex justify-content-between align-items-center">
                <div class="text-muted small" id="queueStats">
                    总文件: 0 个 | 总大小: 0 KB
                </div>
                <button class="btn btn-primary" id="startUploadBtn" disabled>
                    <i class="fas fa-play-circle me-1"></i> 开始上传
                </button>
            </div>
        </div>

        <!-- 上传设置 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-cog me-2"></i>上传设置</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="uploadPath" class="form-label">上传目录</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-folder"></i></span>
                            <input type="text" class="form-control" id="uploadPath" value="/var/www/html" readonly>
                            <button class="btn btn-outline-secondary" type="button"><i class="fas fa-browse"></i></button>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="overwritePolicy" class="form-label">文件冲突策略</label>
                        <select class="form-select" id="overwritePolicy">
                            <option value="rename">自动重命名</option>
                            <option value="overwrite">覆盖现有文件</option>
                            <option value="skip" selected>跳过</option>
                        </select>
                    </div>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="autoUnzip">
                    <label class="form-check-label" for="autoUnzip">
                        自动解压ZIP文件
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="notifyComplete" checked>
                    <label class="form-check-label" for="notifyComplete">
                        上传完成后通知我
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="../libs/bootstrap/5.3.6/index.min.js"></script>
    <script>
        // 文件上传功能实现
        document.addEventListener('DOMContentLoaded', function() {
            const dragArea = document.getElementById('dragArea');
            const fileInput = document.getElementById('fileInput');
            const uploadQueue = document.getElementById('uploadQueue');
            const emptyQueueMessage = document.getElementById('emptyQueueMessage');
            const startUploadBtn = document.getElementById('startUploadBtn');
            const clearQueueBtn = document.getElementById('clearQueueBtn');
            const queueStats = document.getElementById('queueStats');
            let files = [];

            // 拖放事件处理
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dragArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                dragArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dragArea.addEventListener(eventName, unhighlight, false);
            });

            function highlight() {
                dragArea.classList.add('dragover');
            }

            function unhighlight() {
                dragArea.classList.remove('dragover');
            }

            // 处理拖放文件
            dragArea.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const droppedFiles = dt.files;
                handleFiles(droppedFiles);
            }

            // 处理文件选择
            fileInput.addEventListener('change', function() {
                handleFiles(this.files);
            });

            // 点击拖放区域触发文件选择
            dragArea.addEventListener('click', function() {
                if (!event.target.closest('button')) {
                    fileInput.click();
                }
            });

            // 处理文件添加到队列
            function handleFiles(selectedFiles) {
                if (selectedFiles.length === 0) return;

                // 隐藏空队列消息
                if (files.length === 0) {
                    emptyQueueMessage.classList.add('d-none');
                }

                // 添加文件到队列
                Array.from(selectedFiles).forEach(file => {
                    // 检查文件是否已在队列中
                    if (files.some(f => f.name === file.name && f.size === file.size)) {
                        showToast('文件已在队列中: ' + file.name, 'warning');
                        return;
                    }

                    // 检查文件大小
                    if (file.size > 100 * 1024 * 1024) {
                        showToast('文件过大: ' + file.name + ' (超过100MB)', 'danger');
                        return;
                    }

                    files.push(file);
                    addFileToQueueUI(file);
                });

                // 更新队列统计
                updateQueueStats();
                // 启用上传按钮
                startUploadBtn.disabled = false;
            }

            // 添加文件到队列UI
            function addFileToQueueUI(file) {
                const fileSize = formatFileSize(file.size);
                const fileIcon = getFileIcon(file.name);
                const fileItem = document.createElement('li');
                fileItem.className = 'list-group-item upload-queue-item p-3';
                fileItem.setAttribute('data-filename', file.name);
                fileItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="d-flex align-items-center">
                            <div class="me-3 fs-3">${fileIcon}</div>
                            <div>
                                <div class="fw-medium">${file.name}</div>
                                <div class="small text-muted">${fileSize}</div>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-danger remove-file-btn" title="移除">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="upload-status mt-1 small text-muted">等待上传...</div>
                `;
                uploadQueue.appendChild(fileItem);

                // 移除文件按钮事件
                fileItem.querySelector('.remove-file-btn').addEventListener('click', function() {
                    fileItem.remove();
                    // 从文件数组中移除
                    files = files.filter(f => !(f.name === file.name && f.size === file.size));
                    // 更新队列状态
                    updateQueueStats();
                    // 如果队列为空，显示空消息
                    if (files.length === 0) {
                        emptyQueueMessage.classList.remove('d-none');
                        startUploadBtn.disabled = true;
                    }
                });
            }

            // 更新队列统计信息
            function updateQueueStats() {
                const totalFiles = files.length;
                const totalSize = files.reduce((sum, file) => sum + file.size, 0);
                queueStats.textContent = `总文件: ${totalFiles} 个 | 总大小: ${formatFileSize(totalSize)}`;
            }

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 获取文件图标
            function getFileIcon(filename) {
                const ext = filename.split('.').pop().toLowerCase();
                const icons = {
                    'html': '<i class="fas fa-file-code text-primary"></i>',
                    'css': '<i class="fas fa-file-code text-info"></i>',
                    'js': '<i class="fas fa-file-code text-warning"></i>',
                    'jpg': '<i class="fas fa-file-image text-danger"></i>',
                    'jpeg': '<i class="fas fa-file-image text-danger"></i>',
                    'png': '<i class="fas fa-file-image text-danger"></i>',
                    'gif': '<i class="fas fa-file-image text-danger"></i>',
                    'txt': '<i class="fas fa-file-alt text-muted"></i>',
                    'zip': '<i class="fas fa-file-archive text-primary"></i>',
                    'rar': '<i class="fas fa-file-archive text-primary"></i>',
                    'pdf': '<i class="fas fa-file-pdf text-danger"></i>'
                };
                return icons[ext] || '<i class="fas fa-file text-secondary"></i>';
            }

            // 清空队列
            clearQueueBtn.addEventListener('click', function() {
                if (files.length === 0) return;
                if (confirm('确定要清空上传队列吗？')) {
                    // 清空文件数组
                    files = [];
                    // 清空队列UI
                    uploadQueue.innerHTML = '';
                    // 显示空队列消息
                    uploadQueue.appendChild(emptyQueueMessage);
                    emptyQueueMessage.classList.remove('d-none');
                    // 更新统计
                    updateQueueStats();
                    // 禁用上传按钮
                    startUploadBtn.disabled = true;
                }
            });

            // 开始上传
            startUploadBtn.addEventListener('click', function() {
                this.disabled = true;
                clearQueueBtn.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> 上传中...';

                // 模拟上传过程
                const fileItems = document.querySelectorAll('.upload-queue-item');
                let completed = 0;

                fileItems.forEach((item, index) => {
                    setTimeout(() => {
                        const progressBar = item.querySelector('.progress-bar');
                        const statusText = item.querySelector('.upload-status');
                        let progress = 0;
                        const interval = setInterval(() => {
                            progress += Math.random() * 10;
                            if (progress >= 100) {
                                progress = 100;
                                clearInterval(interval);
                                completed++;
                                statusText.textContent = '上传完成';
                                statusText.className = 'upload-status mt-1 small text-success';
                                progressBar.className = 'progress-bar bg-success';

                                // 检查是否所有文件都已上传完成
                                if (completed === fileItems.length) {
                                    startUploadBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> 全部完成';
                                    startUploadBtn.className = 'btn btn-success';
                                    clearQueueBtn.disabled = false;
                                    if (document.getElementById('notifyComplete').checked) {
                                        showToast(`上传完成，共 ${completed} 个文件`, 'success');
                                    }
                                }
                            }
                            progressBar.style.width = progress + '%';
                            progressBar.setAttribute('aria-valuenow', progress);
                            statusText.textContent = `上传中... ${Math.round(progress)}%`;
                        }, 200);
                    }, index * 1500);
                });
            });

            // 显示提示消息
            function showToast(message, type = 'info') {
                const toastEl = document.createElement('div');
                toastEl.className = `toast align-items-center text-white bg-${type} border-0 position-fixed bottom-0 end-0 m-3`;
                toastEl.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                `;
                document.body.appendChild(toastEl);
                const toast = new bootstrap.Toast(toastEl);
                toast.show();

                // 3秒后自动移除
                setTimeout(() => { toastEl.remove(); }, 3000);
            }
        });
    </script>
</body>
</html>