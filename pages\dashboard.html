<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控面板</title>
    <!-- 引入Bootstrap CSS -->
    <link rel="stylesheet" href="../libs/bootstrap/5.3.6/index.min.css">
    <!-- 引入FontAwesome -->
    <link rel="stylesheet" href="../libs/fontawesome/6.7.2/css/all.min.css">
    <style>
        .sidebar {
            height: 100vh;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        .status-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
        }
        .status-running {
            background-color: #28a745;
        }
        .status-stopped {
            background-color: #dc3545;
        }
        .status-warning {
            background-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 d-none d-md-block sidebar p-3">
                <h5 class="text-center mb-4"><i class="fas fa-server me-2"></i>服务器监控</h5>
                <div class="recent-dirs mb-4">
                    <h6 class="text-muted"><i class="fas fa-history me-2"></i>最近访问</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item"><a href="nginx_status.html" class="text-decoration-none"><i class="fas fa-network-wired me-2"></i>Nginx状态</a></li>
                        <li class="list-group-item"><a href="file_manager.html" class="text-decoration-none"><i class="fas fa-folder me-2"></i>文件管理</a></li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-10 ms-sm-auto px-md-4 py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2"><i class="fas fa-tachometer-alt me-2"></i>系统监控面板</h1>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary"><i class="fas fa-sync-alt me-1"></i>刷新</button>
                        <button class="btn btn-outline-secondary"><i class="fas fa-cog me-1"></i>设置</button>
                    </div>
                </div>

                <!-- 服务状态卡片 -->
                <div class="row row-cols-1 row-cols-md-3 g-4 mb-4">
                    <!-- Nginx状态卡片 -->
                    <div class="col">
                        <div class="card h-100 shadow-sm border-left-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="card-title"><i class="fas fa-network-wired me-2"></i>Nginx服务</h5>
                                    <span class="status-indicator status-running" title="运行中"></span>
                                </div>
                                <p class="card-text text-muted">运行时间: 2天8小时</p>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 65%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent d-flex justify-content-between">
                                <small class="text-muted">最后检查: 刚刚</small>
                                <a href="nginx_status.html" class="btn btn-sm btn-outline-primary"><i class="fas fa-arrow-right me-1"></i>详情</a>
                            </div>
                        </div>
                    </div>

                    <!-- MySQL状态卡片 -->
                    <div class="col">
                        <div class="card h-100 shadow-sm border-left-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="card-title"><i class="fas fa-database me-2"></i>MySQL服务</h5>
                                    <span class="status-indicator status-running" title="运行中"></span>
                                </div>
                                <p class="card-text text-muted">连接数: 12/100</p>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 12%" aria-valuenow="12" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent d-flex justify-content-between">
                                <small class="text-muted">最后检查: 刚刚</small>
                                <a href="#" class="btn btn-sm btn-outline-success"><i class="fas fa-arrow-right me-1"></i>详情</a>
                            </div>
                        </div>
                    </div>

                    <!-- Redis状态卡片 -->
                    <div class="col">
                        <div class="card h-100 shadow-sm border-left-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="card-title"><i class="fas fa-memory me-2"></i>Redis服务</h5>
                                    <span class="status-indicator status-warning" title="警告"></span>
                                </div>
                                <p class="card-text text-muted">内存使用: 85%</p>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 85%" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent d-flex justify-content-between">
                                <small class="text-muted">最后检查: 刚刚</small>
                                <a href="#" class="btn btn-sm btn-outline-warning"><i class="fas fa-arrow-right me-1"></i>详情</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 资源使用情况 -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card shadow-sm mb-4">
                            <div class="card-header">
                                <ul class="nav nav-tabs card-header-tabs">
                                    <li class="nav-item">
                                        <a class="nav-link active" href="#cpu-tab"><i class="fas fa-microchip me-1"></i>CPU占用</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#mem-tab"><i class="fas fa-memory me-1"></i>内存使用</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#disk-tab"><i class="fas fa-hdd me-1"></i>磁盘IO</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height: 300px;">
                                    <!-- 图表将通过JavaScript渲染 -->
                                    <div class="d-flex justify-content-center align-items-center h-100">
                                        <div class="text-center">
                                            <i class="fas fa-chart-line fa-3x text-muted mb-2"></i>
                                            <p class="text-muted">加载图表数据中...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统信息 -->
                    <div class="col-md-4">
                        <div class="card shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>系统信息</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span><i class="fas fa-server me-2 text-muted"></i>服务器</span>
                                        <span class="font-weight-bold">Linux 4.15.0</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span><i class="fas fa-microchip me-2 text-muted"></i>CPU</span>
                                        <span>4核 Intel(R) Xeon(R) CPU</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span><i class="fas fa-memory me-2 text-muted"></i>内存</span>
                                        <span>16GB / 32GB</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span><i class="fas fa-hdd me-2 text-muted"></i>磁盘</span>
                                        <span>120GB / 500GB</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span><i class="fas fa-clock me-2 text-muted"></i>运行时间</span>
                                        <span>5天12小时</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="../libs/bootstrap/5.3.6/index.min.js"></script>
    <script>
        // 简单的页面交互示例
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 模拟状态刷新
            const refreshBtn = document.querySelector('.btn-outline-primary');
            refreshBtn.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新';
                    // 这里可以添加实际的刷新逻辑
                }, 1000);
            });
        });
    </script>
</body>
</html>